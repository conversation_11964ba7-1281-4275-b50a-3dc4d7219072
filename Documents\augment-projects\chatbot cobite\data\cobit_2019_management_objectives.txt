COBIT 2019 - Objectifs de Gestion par Domaine

Les objectifs de gestion COBIT 2019 sont organisés en quatre domaines principaux, chacun contenant plusieurs objectifs spécifiques.

DOMAINE APO - ALIGNER, PLANIFIER ET ORGANISER

APO01 - G<PERSON>rer le cadre de gestion I&T
Établir et maintenir un cadre de gestion I&T qui fournit une approche cohérente pour la gestion de l'I&T dans toute l'entreprise.

APO02 - Gérer la stratégie
Fournir une approche cohérente pour développer des stratégies I&T qui soutiennent et étendent les stratégies et objectifs d'entreprise.

APO03 - Gérer l'architecture d'entreprise
Établir une architecture d'entreprise commune comprenant les processus métier, l'information, les données, les applications et l'infrastructure technologique.

APO04 - Gérer l'innovation
Maintenir une conscience des technologies émergentes et des opportunités d'innovation, et planifier comment tirer parti de l'innovation pour répondre aux besoins de l'entreprise.

APO05 - <PERSON><PERSON><PERSON> le portefeuille
Exécuter la direction stratégique définie pour l'I&T en gérant les programmes et projets I&T en ligne avec la stratégie d'entreprise et en surveillant leur performance.

APO06 - Gérer le budget et les coûts
Gérer les activités financières I&T couvrant la budgétisation, la gestion des coûts et l'analyse coûts-bénéfices pour l'I&T.

APO07 - Gérer les ressources humaines
Acquérir, développer et maintenir une main-d'œuvre compétente et motivée pour créer et livrer des services I&T alignés sur la stratégie d'entreprise.

APO08 - Gérer les relations
Gérer la relation entre l'entreprise et l'I&T de manière formalisée et transparente, assurant un focus sur la réalisation d'un résultat mutuellement bénéfique.

APO09 - Gérer les accords de service
Aligner les services I&T et les niveaux de service avec les besoins et attentes de l'entreprise, incluant l'identification, la spécification, la conception, la publication, l'accord et la surveillance des services I&T et des niveaux de service.

APO10 - Gérer les fournisseurs
Gérer les services I&T fournis par tous types de fournisseurs pour répondre aux besoins de l'entreprise, incluant la sélection des fournisseurs, la gestion des relations, la gestion des contrats et l'examen et surveillance de la performance des fournisseurs.

APO11 - Gérer la qualité
Définir et communiquer les exigences de qualité, les critères et les métriques; mettre en place des pratiques d'assurance et de contrôle qualité; et surveiller et rapporter sur la qualité.

APO12 - Gérer les risques
Identifier, évaluer et réduire continuellement les risques liés à l'I&T dans des niveaux de tolérance acceptables selon la stratégie d'entreprise.

APO13 - Gérer la sécurité
Définir, opérer et surveiller un système pour la gestion de la sécurité de l'information.

APO14 - Gérer les données
Gérer les données comme un actif d'entreprise, incluant l'établissement de concepts de données appropriés, la syntaxe et la sémantique, les mécanismes d'intégrité des données et les structures de données.

DOMAINE BAI - CONSTRUIRE, ACQUÉRIR ET IMPLÉMENTER

BAI01 - Gérer les programmes et projets
Gérer tous les programmes et projets depuis l'idée initiale jusqu'à l'achèvement final d'une manière qui soutient les objectifs d'entreprise.

BAI02 - Gérer la définition des exigences
Identifier des solutions et analyser les exigences avant l'acquisition ou la création pour s'assurer qu'elles sont en ligne avec les exigences d'entreprise.

BAI03 - Gérer l'identification et la construction des solutions
Établir et maintenir des solutions identifiées en ligne avec les exigences d'entreprise couvrant la conception, le développement, l'approvisionnement/l'achat et le partenariat avec des fournisseurs/vendeurs.

BAI04 - Gérer la disponibilité et la capacité
Équilibrer les besoins actuels et futurs de disponibilité, de performance et de capacité avec une provision de service rentable.

BAI05 - Gérer l'habilitation du changement organisationnel
Maximiser la probabilité de mise en œuvre réussie du changement organisationnel durable lié à l'I&T.

BAI06 - Gérer les changements I&T
Gérer tous les changements I&T d'une manière contrôlée, incluant les changements d'urgence et de maintenance liés aux processus métier, aux applications et à l'infrastructure.

BAI07 - Gérer l'acceptation du changement et la transition
Accepter formellement et rendre opérationnelles les nouvelles solutions, incluant les plans de mise en œuvre/déploiement, la conversion des données, les tests d'acceptation, la communication, la préparation de la version, la promotion vers la production et l'examen post-mise en œuvre.

BAI08 - Gérer la connaissance
Maintenir la disponibilité d'informations pertinentes, actuelles, validées et fiables pour soutenir toutes les activités de processus et faciliter la prise de décision.

BAI09 - Gérer les actifs
Gérer les actifs I&T tout au long de leur cycle de vie pour s'assurer que leur utilisation livre de la valeur au coût optimal, qu'ils restent opérationnels, qu'ils sont comptabilisés, protégés physiquement et que ceux qui ne sont plus productifs ou qui ont atteint la fin de leur vie utile sont retirés du service.

BAI10 - Gérer la configuration
Maintenir l'intégrité des composants de configuration et les rendre disponibles aux autres processus selon les besoins.

BAI11 - Gérer les projets
Gérer l'activité de projet pour l'ensemble du cycle de vie du projet en établissant un programme et un projet de gestion qui utilise des méthodologies de gestion de projet appropriées.

DOMAINE DSS - LIVRER, SERVIR ET SOUTENIR

DSS01 - Gérer les opérations
Coordonner et exécuter les activités et procédures opérationnelles requises pour livrer des services I&T internes et externalisés, incluant l'exécution de procédures opérationnelles prédéfinies et les activités de surveillance requises.

DSS02 - Gérer les demandes de service et les incidents
Fournir une réponse rapide et efficace aux demandes des utilisateurs et à la résolution de tous types d'incidents. Restaurer le service normal aussi rapidement que possible.

DSS03 - Gérer les problèmes
Identifier et classer les problèmes et leurs causes racines et fournir une résolution rapide pour minimiser l'impact sur l'entreprise.

DSS04 - Gérer la continuité
Développer et maintenir un plan de continuité I&T, effectuer des sauvegardes régulières et assurer une récupération rapide des services I&T en cas d'interruption.

DSS05 - Gérer les services de sécurité
Protéger les informations de l'entreprise pour maintenir le niveau de risque de sécurité de l'information acceptable pour l'entreprise.

DSS06 - Gérer les contrôles de processus métier
Définir et maintenir des contrôles de processus métier appropriés pour s'assurer que l'information liée aux processus métier supportés par l'I&T répond à tous les exigences de contrôle interne pertinentes.

DOMAINE MEA - SURVEILLER, ÉVALUER ET APPRÉCIER

MEA01 - Surveiller, évaluer et apprécier la performance et la conformité
Collecter, valider et évaluer les métriques d'entreprise, I&T et de processus pour s'assurer que l'entreprise atteint ses objectifs et que les processus se conforment aux exigences internes et externes.

MEA02 - Surveiller, évaluer et apprécier le système de contrôle interne
Surveiller et évaluer continuellement l'efficacité et l'efficience du système de contrôle interne pour la gouvernance, la gestion des risques et le contrôle des processus I&T et d'entreprise.

MEA03 - Surveiller, évaluer et apprécier la conformité aux exigences externes
Évaluer que les processus I&T et d'entreprise se conforment aux lois, réglementations et exigences contractuelles, obtenir l'assurance que les exigences ont été identifiées et se conformer à celles-ci, et intégrer la conformité I&T avec la conformité d'entreprise globale.

MEA04 - Fournir la gouvernance, l'assurance et l'audit I&T
Fournir une fonction d'assurance indépendante pour l'entreprise et les parties prenantes externes que l'I&T et ses processus répondent aux objectifs convenus, se conforment aux politiques et sont gérés efficacement.

Facteurs de conception COBIT 2019

COBIT 2019 introduit le concept de facteurs de conception qui permettent aux organisations d'adapter le framework à leurs besoins spécifiques :

1. Stratégie d'entreprise
- Vision, mission et valeurs de l'organisation
- Objectifs stratégiques et priorités
- Modèle d'affaires et secteur d'activité

2. Objectifs d'entreprise
- Objectifs financiers et opérationnels
- Objectifs de croissance et d'innovation
- Objectifs de conformité et de risque

3. Profil de risque
- Appétit et tolérance au risque
- Types de risques auxquels l'organisation est exposée
- Maturité de la gestion des risques

4. Problèmes I&T
- Défis technologiques actuels
- Lacunes en matière de capacités I&T
- Incidents et problèmes récurrents

5. Facteurs de menace
- Menaces de cybersécurité
- Risques réglementaires
- Risques opérationnels

6. Exigences de conformité
- Réglementations sectorielles
- Standards internationaux
- Politiques internes

7. Rôle de l'I&T
- I&T comme support
- I&T comme facilitateur
- I&T comme moteur de l'entreprise

8. Modèle de sourcing
- Interne vs externalisé
- Cloud vs sur site
- Modèles hybrides

9. Méthodes de mise en œuvre
- Approche agile vs traditionnelle
- Mise en œuvre progressive vs complète
- Pilotes vs déploiement global

10. Taille et structure de l'entreprise
- Petite, moyenne ou grande entreprise
- Structure organisationnelle
- Complexité géographique

Ces facteurs de conception permettent aux organisations de personnaliser leur approche COBIT 2019 pour maximiser son efficacité et sa pertinence.
