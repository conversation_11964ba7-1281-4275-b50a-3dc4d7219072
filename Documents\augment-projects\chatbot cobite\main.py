"""
Chatbot RAG COBIT 2019 - API FastAPI avec Ollama et LlamaIndex
Auteur: Assistant IA
Description: Chatbot local pour répondre aux questions sur COBIT 2019
"""

import os
import logging
from typing import Dict, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

from llama_index.core import (
    VectorStoreIndex, 
    SimpleDirectoryReader, 
    Settings,
    StorageContext,
    load_index_from_storage
)
from llama_index.llms.ollama import Ollama
from llama_index.embeddings.ollama import OllamaEmbedding

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration des chemins
DATA_DIR = Path("data")
PERSIST_DIR = Path("storage")

# Modèles de données Pydantic
class QueryRequest(BaseModel):
    question: str

class QueryResponse(BaseModel):
    response: str

class COBITChatbot:
    """Classe principale du chatbot COBIT 2019"""
    
    def __init__(self):
        self.index = None
        self.query_engine = None
        self._setup_llama_index()
        self._load_or_create_index()
    
    def _setup_llama_index(self):
        """Configuration de LlamaIndex avec Ollama"""
        try:
            # Configuration du LLM Ollama
            Settings.llm = Ollama(
                model="mistral",  # Vous pouvez changer pour llama3 si préféré
                base_url="http://localhost:11434",
                request_timeout=120.0
            )
            
            # Configuration des embeddings Ollama
            Settings.embed_model = OllamaEmbedding(
                model_name="nomic-embed-text",
                base_url="http://localhost:11434",
                ollama_additional_kwargs={"mirostat": 0}
            )
            
            logger.info("Configuration LlamaIndex avec Ollama réussie")
            
        except Exception as e:
            logger.error(f"Erreur lors de la configuration LlamaIndex: {e}")
            raise
    
    def _load_or_create_index(self):
        """Charge l'index existant ou en crée un nouveau"""
        try:
            if PERSIST_DIR.exists():
                # Charger l'index existant
                logger.info("Chargement de l'index existant...")
                storage_context = StorageContext.from_defaults(persist_dir=str(PERSIST_DIR))
                self.index = load_index_from_storage(storage_context)
                logger.info("Index chargé avec succès")
            else:
                # Créer un nouvel index
                logger.info("Création d'un nouvel index...")
                self._create_new_index()
            
            # Créer le moteur de requête
            self.query_engine = self.index.as_query_engine(
                similarity_top_k=3,
                response_mode="compact"
            )
            
        except Exception as e:
            logger.error(f"Erreur lors du chargement/création de l'index: {e}")
            raise
    
    def _create_new_index(self):
        """Crée un nouvel index à partir des documents"""
        if not DATA_DIR.exists():
            raise FileNotFoundError(f"Le dossier {DATA_DIR} n'existe pas")
        
        # Vérifier qu'il y a des fichiers .txt
        txt_files = list(DATA_DIR.glob("*.txt"))
        if not txt_files:
            raise FileNotFoundError(f"Aucun fichier .txt trouvé dans {DATA_DIR}")
        
        logger.info(f"Lecture de {len(txt_files)} fichiers...")
        
        # Charger les documents
        documents = SimpleDirectoryReader(
            input_dir=str(DATA_DIR),
            required_exts=[".txt"],
            recursive=True
        ).load_data()
        
        logger.info(f"Chargé {len(documents)} documents")
        
        # Créer l'index
        self.index = VectorStoreIndex.from_documents(documents)
        
        # Sauvegarder l'index
        PERSIST_DIR.mkdir(exist_ok=True)
        self.index.storage_context.persist(persist_dir=str(PERSIST_DIR))
        
        logger.info("Index créé et sauvegardé avec succès")
    
    def query(self, question: str) -> str:
        """Traite une question et retourne la réponse"""
        try:
            if not self.query_engine:
                raise RuntimeError("Le moteur de requête n'est pas initialisé")
            
            logger.info(f"Question reçue: {question}")
            
            # Construire le prompt avec contexte COBIT
            enhanced_question = f"""
            En tant qu'expert COBIT 2019, réponds à la question suivante en utilisant uniquement 
            les informations du framework COBIT 2019 fournies dans les documents.
            
            Question: {question}
            
            Assure-toi de:
            - Utiliser uniquement les informations COBIT 2019 disponibles
            - Être précis et factuel
            - Mentionner les objectifs de gouvernance ou de gestion pertinents si applicable
            - Structurer ta réponse de manière claire
            """
            
            # Exécuter la requête
            response = self.query_engine.query(enhanced_question)
            
            logger.info("Réponse générée avec succès")
            return str(response)
            
        except Exception as e:
            logger.error(f"Erreur lors du traitement de la question: {e}")
            raise

# Initialisation du chatbot
chatbot = COBITChatbot()

# Création de l'application FastAPI
app = FastAPI(
    title="COBIT 2019 RAG Chatbot",
    description="Chatbot local basé sur Ollama pour répondre aux questions sur COBIT 2019",
    version="1.0.0"
)

@app.get("/")
async def root():
    """Point d'entrée de l'API"""
    return {
        "message": "COBIT 2019 RAG Chatbot API",
        "status": "active",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Vérification de l'état de santé de l'API"""
    try:
        # Vérifier que le chatbot est initialisé
        if chatbot.query_engine is None:
            raise RuntimeError("Chatbot non initialisé")
        
        return {
            "status": "healthy",
            "ollama_connected": True,
            "index_loaded": True
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service non disponible: {str(e)}")

@app.post("/query", response_model=QueryResponse)
async def query_cobit(request: QueryRequest) -> QueryResponse:
    """
    Point d'entrée principal pour les questions COBIT 2019
    
    Args:
        request: Objet contenant la question
        
    Returns:
        QueryResponse: Réponse générée par le chatbot
    """
    try:
        if not request.question.strip():
            raise HTTPException(status_code=400, detail="La question ne peut pas être vide")
        
        # Traiter la question
        response = chatbot.query(request.question)
        
        return QueryResponse(response=response)
        
    except Exception as e:
        logger.error(f"Erreur lors du traitement de la requête: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur interne: {str(e)}")

if __name__ == "__main__":
    # Vérifier que Ollama est accessible
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            logger.warning("Ollama ne semble pas être accessible sur localhost:11434")
    except Exception as e:
        logger.warning(f"Impossible de vérifier Ollama: {e}")
    
    # Lancer le serveur
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
